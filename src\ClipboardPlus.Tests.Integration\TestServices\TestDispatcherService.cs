using System;
using System.Threading.Tasks;
using ClipboardPlus.Services.Interfaces;

namespace ClipboardPlus.Tests.Integration.TestServices
{
    /// <summary>
    /// Implémentation de test pour IDispatcherService.
    /// Exécute toutes les actions de manière synchrone sur le thread de test.
    /// 
    /// Cette implémentation respecte le Principe d'Inversion des Dépendances (DIP) :
    /// - Permet de tester le code sans dépendre du dispatcher WPF
    /// - Exécute tout de manière synchrone pour simplifier les tests
    /// - Évite les problèmes de threading dans l'environnement de test
    /// </summary>
    public class TestDispatcherService : IDispatcherService
    {
        /// <summary>
        /// Dans l'environnement de test, nous considérons toujours avoir accès au "dispatcher".
        /// </summary>
        /// <returns>Toujours true pour simplifier les tests.</returns>
        public bool CheckAccess()
        {
            return true;
        }

        /// <summary>
        /// Exécute l'action de manière synchrone sur le thread de test.
        /// </summary>
        /// <param name="action">L'action à exécuter.</param>
        public void Invoke(Action action)
        {
            if (action == null) throw new ArgumentNullException(nameof(action));
            
            // Exécution directe sur le thread de test
            action();
        }

        /// <summary>
        /// Exécute la fonction de manière synchrone sur le thread de test et retourne le résultat.
        /// </summary>
        /// <typeparam name="T">Le type de retour de la fonction.</typeparam>
        /// <param name="func">La fonction à exécuter.</param>
        /// <returns>Le résultat de la fonction.</returns>
        public T Invoke<T>(Func<T> func)
        {
            if (func == null) throw new ArgumentNullException(nameof(func));
            
            // Exécution directe sur le thread de test
            return func();
        }

        /// <summary>
        /// Exécute l'action de manière "asynchrone" (mais en réalité synchrone) sur le thread de test.
        /// </summary>
        /// <param name="action">L'action à exécuter.</param>
        /// <returns>Une tâche complétée.</returns>
        public Task InvokeAsync(Action action)
        {
            if (action == null) throw new ArgumentNullException(nameof(action));
            
            // Exécution directe sur le thread de test
            action();
            return Task.CompletedTask;
        }

        /// <summary>
        /// Exécute la fonction de manière "asynchrone" (mais en réalité synchrone) sur le thread de test.
        /// </summary>
        /// <typeparam name="T">Le type de retour de la fonction.</typeparam>
        /// <param name="func">La fonction à exécuter.</param>
        /// <returns>Une tâche complétée avec le résultat.</returns>
        public Task<T> InvokeAsync<T>(Func<T> func)
        {
            if (func == null) throw new ArgumentNullException(nameof(func));
            
            // Exécution directe sur le thread de test
            var result = func();
            return Task.FromResult(result);
        }

        /// <summary>
        /// Exécute la tâche de manière synchrone sur le thread de test.
        /// </summary>
        /// <param name="taskFunc">La fonction qui retourne une tâche à exécuter.</param>
        /// <returns>La tâche résultante.</returns>
        public Task InvokeAsync(Func<Task> taskFunc)
        {
            if (taskFunc == null) throw new ArgumentNullException(nameof(taskFunc));
            
            // Exécution directe sur le thread de test
            return taskFunc();
        }

        /// <summary>
        /// Exécute la tâche de manière synchrone sur le thread de test et retourne le résultat.
        /// </summary>
        /// <typeparam name="T">Le type de retour de la tâche.</typeparam>
        /// <param name="taskFunc">La fonction qui retourne une tâche à exécuter.</param>
        /// <returns>La tâche résultante avec le résultat.</returns>
        public Task<T> InvokeAsync<T>(Func<Task<T>> taskFunc)
        {
            if (taskFunc == null) throw new ArgumentNullException(nameof(taskFunc));
            
            // Exécution directe sur le thread de test
            return taskFunc();
        }
    }
}
