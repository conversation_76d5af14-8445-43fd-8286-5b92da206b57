using System;
using System.Threading.Tasks;
using System.Windows.Input;
using NUnit.Framework;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Services;

namespace ClipboardPlus.Tests.STA
{
    /// <summary>
    /// Tests de caractérisation d'intégration pour InitializeAsync dans le contexte d'ApplicationLifetimeManager.
    /// Ces tests capturent le comportement actuel EXACT de l'intégration avant refactorisation.
    /// </summary>
    [TestFixture]
    [Apartment(System.Threading.ApartmentState.STA)]
    public class InitializeAsyncIntegrationCharacterizationTests
    {
        private ServiceProvider _serviceProvider = null!;
        private Mock<ILoggingService> _mockLoggingService = null!;
        private Mock<ISettingsManager> _mockSettingsManager = null!;
        private Mock<IGlobalShortcutService> _mockShortcutService = null!;
        private Mock<IClipboardListenerService> _mockClipboardListener = null!;
        private Mock<IClipboardHistoryManager> _mockClipboardHistoryManager = null!;
        private Mock<ISystemTrayService> _mockSystemTrayService = null!;

        [SetUp]
        public void SetUp()
        {
            var services = new ServiceCollection();

            // Créer les mocks
            _mockLoggingService = new Mock<ILoggingService>();
            _mockSettingsManager = new Mock<ISettingsManager>();
            _mockShortcutService = new Mock<IGlobalShortcutService>();
            _mockClipboardListener = new Mock<IClipboardListenerService>();
            _mockClipboardHistoryManager = new Mock<IClipboardHistoryManager>();
            _mockSystemTrayService = new Mock<ISystemTrayService>();

            // Configuration des mocks pour l'initialisation
            _mockSettingsManager.Setup(s => s.LoadSettingsAsync()).Returns(Task.CompletedTask);
            _mockSettingsManager.Setup(s => s.ShortcutKeyCombination).Returns("Ctrl+Alt+V");
            _mockShortcutService.Setup(s => s.InitializeAsync(It.IsAny<KeyCombination>())).Returns(Task.CompletedTask);
            _mockClipboardListener.Setup(s => s.StartListening()).Returns(true);
            _mockClipboardListener.Setup(c => c.IsListening).Returns(true);

            // Configuration des mocks pour la fermeture
            _mockClipboardListener.Setup(s => s.StopListening());
            _mockShortcutService.Setup(s => s.UnregisterShortcut());
            _mockClipboardHistoryManager.Setup(m => m.PurgeOrphanedItemsAsync()).Returns(Task.FromResult(0));
            
            // Enregistrer les services
            services.AddSingleton<IApplicationLifetimeManager, ApplicationLifetimeManager>();
            services.AddSingleton(_mockLoggingService.Object);
            services.AddSingleton(_mockSettingsManager.Object);
            services.AddSingleton(_mockShortcutService.Object);
            services.AddSingleton(_mockClipboardListener.Object);
            services.AddSingleton(_mockClipboardHistoryManager.Object);
            services.AddSingleton(_mockSystemTrayService.Object);

            _serviceProvider = services.BuildServiceProvider();
        }

        [TearDown]
        public void TearDown()
        {
            _serviceProvider?.Dispose();
        }

        #region Tests de Caractérisation du Flux d'Initialisation

        [Test]
        [Description("CARACTÉRISATION: ApplicationLifetimeManager doit appeler InitializeAsync exactement une fois")]
        public async Task ApplicationLifetimeManager_InitializeServices_CallsInitializeAsyncOnce()
        {
            // Arrange
            var lifetimeManager = _serviceProvider.GetRequiredService<IApplicationLifetimeManager>();

            // Act
            await lifetimeManager.InitializeServices(_serviceProvider, (s, e) => { }, (s, e) => { });

            // Assert
            _mockShortcutService.Verify(s => s.InitializeAsync(It.IsAny<KeyCombination>()), Times.Once, 
                "InitializeAsync devrait être appelé exactement une fois");
        }

        [Test]
        [Description("CARACTÉRISATION: InitializeAsync doit être appelé avec la bonne KeyCombination")]
        public async Task ApplicationLifetimeManager_InitializeServices_CallsInitializeAsyncWithCorrectShortcut()
        {
            // Arrange
            var lifetimeManager = _serviceProvider.GetRequiredService<IApplicationLifetimeManager>();
            KeyCombination? capturedShortcut = null;

            _mockShortcutService.Setup(s => s.InitializeAsync(It.IsAny<KeyCombination>()))
                .Callback<KeyCombination>(shortcut => capturedShortcut = shortcut)
                .Returns(Task.CompletedTask);

            // Act
            await lifetimeManager.InitializeServices(_serviceProvider, (s, e) => { }, (s, e) => { });

            // Assert
            Assert.That(capturedShortcut, Is.Not.Null, "Une KeyCombination devrait avoir été passée");
            Assert.That(capturedShortcut!.Modifiers, Is.EqualTo(ModifierKeys.Control | ModifierKeys.Alt));
            Assert.That(capturedShortcut.Key, Is.EqualTo(Key.V));
        }

        [Test]
        [Description("CARACTÉRISATION: L'ordre d'initialisation doit être respecté")]
        public async Task ApplicationLifetimeManager_InitializeServices_RespectsInitializationOrder()
        {
            // Arrange
            var lifetimeManager = _serviceProvider.GetRequiredService<IApplicationLifetimeManager>();
            var callOrder = new List<string>();

            _mockSettingsManager.Setup(s => s.LoadSettingsAsync())
                .Callback(() => callOrder.Add("LoadSettings"))
                .Returns(Task.CompletedTask);

            _mockShortcutService.Setup(s => s.InitializeAsync(It.IsAny<KeyCombination>()))
                .Callback(() => callOrder.Add("InitializeAsync"))
                .Returns(Task.CompletedTask);

            _mockClipboardListener.Setup(s => s.StartListening())
                .Callback(() => callOrder.Add("StartListening"))
                .Returns(true);

            // Act
            await lifetimeManager.InitializeServices(_serviceProvider, (s, e) => { }, (s, e) => { });

            // Assert
            Assert.That(callOrder.Count, Is.GreaterThanOrEqualTo(3));
            Assert.That(callOrder[0], Is.EqualTo("LoadSettings"), "LoadSettings devrait être appelé en premier");
            Assert.That(callOrder.IndexOf("InitializeAsync"), Is.LessThan(callOrder.IndexOf("StartListening")), 
                "InitializeAsync devrait être appelé avant StartListening");
        }

        #endregion

        #region Tests de Gestion d'Erreurs

        [Test]
        [Description("CARACTÉRISATION: Une exception dans InitializeAsync ne doit pas arrêter l'initialisation")]
        public void ApplicationLifetimeManager_InitializeServices_ContinuesWhenInitializeAsyncThrows()
        {
            // Arrange
            var lifetimeManager = _serviceProvider.GetRequiredService<IApplicationLifetimeManager>();
            _mockShortcutService.Setup(s => s.InitializeAsync(It.IsAny<KeyCombination>()))
                .ThrowsAsync(new InvalidOperationException("Test exception"));

            // Act & Assert
            Assert.DoesNotThrowAsync(async () =>
                await lifetimeManager.InitializeServices(_serviceProvider, (s, e) => { }, (s, e) => { }));

            // Vérifier que les autres services ont quand même été initialisés
            _mockClipboardListener.Verify(s => s.StartListening(), Times.AtLeastOnce,
                "StartListening devrait avoir été appelé malgré l'exception");
        }

        [Test]
        [Description("CARACTÉRISATION: Les erreurs InitializeAsync doivent être loggées")]
        public async Task ApplicationLifetimeManager_InitializeServices_LogsInitializeAsyncErrors()
        {
            // Arrange
            var lifetimeManager = _serviceProvider.GetRequiredService<IApplicationLifetimeManager>();
            var testException = new InvalidOperationException("Test exception");
            _mockShortcutService.Setup(s => s.InitializeAsync(It.IsAny<KeyCombination>()))
                .ThrowsAsync(testException);

            // Act
            await lifetimeManager.InitializeServices(_serviceProvider, (s, e) => { }, (s, e) => { });

            // Assert
            _mockLoggingService.Verify(
                l => l.LogError(It.Is<string>(msg => msg.Contains("Erreur lors de l'initialisation du raccourci")), 
                               It.Is<Exception>(ex => ex == testException)), 
                Times.Once, 
                "L'erreur devrait être loggée");
        }

        #endregion

        #region Tests de Performance

        [Test]
        [Description("CARACTÉRISATION: L'initialisation complète doit se terminer dans un délai raisonnable")]
        [Timeout(10000)] // 10 secondes maximum
        public async Task ApplicationLifetimeManager_InitializeServices_CompletesWithinTimeout()
        {
            // Arrange
            var lifetimeManager = _serviceProvider.GetRequiredService<IApplicationLifetimeManager>();

            // Act
            await lifetimeManager.InitializeServices(_serviceProvider, (s, e) => { }, (s, e) => { });

            // Assert
            Assert.Pass("L'initialisation s'est terminée dans le délai imparti");
        }

        #endregion

        #region Tests de Configuration

        [Test]
        [Description("CARACTÉRISATION: InitializeAsync doit être appelé même avec un raccourci invalide")]
        public async Task ApplicationLifetimeManager_InitializeServices_CallsInitializeAsyncWithInvalidShortcut()
        {
            // Arrange
            _mockSettingsManager.Setup(s => s.ShortcutKeyCombination).Returns("InvalidShortcut");
            var lifetimeManager = _serviceProvider.GetRequiredService<IApplicationLifetimeManager>();

            // Act
            await lifetimeManager.InitializeServices(_serviceProvider, (s, e) => { }, (s, e) => { });

            // Assert - InitializeAsync ne devrait PAS être appelé avec un raccourci invalide
            _mockShortcutService.Verify(s => s.InitializeAsync(It.IsAny<KeyCombination>()), Times.Never, 
                "InitializeAsync ne devrait pas être appelé avec un raccourci invalide");
        }

        #endregion
    }
}
