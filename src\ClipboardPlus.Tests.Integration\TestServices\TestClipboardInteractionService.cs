using System;
using System.Collections.Specialized;
using System.Threading.Tasks;
using System.Windows.Media.Imaging;
using ClipboardPlus.Core.DataModels;
using ClipboardPlus.Core.Services;

namespace ClipboardPlus.Tests.Integration.TestServices
{
    /// <summary>
    /// Implémentation de test pour IClipboardInteractionService qui simule l'interaction avec le clipboard
    /// sans nécessiter un thread STA ou accéder au vrai clipboard Windows.
    /// </summary>
    public class TestClipboardInteractionService : IClipboardInteractionService
    {
        private readonly ILoggingService _loggingService;
        private string _simulatedClipboardContent = string.Empty;

        public TestClipboardInteractionService(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        }

        public async Task<ClipboardData?> GetClipboardContentAsync()
        {
            _loggingService.LogInfo("TestClipboardInteractionService: Simulation de la récupération du contenu complet du clipboard");

            // Simuler l'opération asynchrone
            await Task.Delay(10);

            var clipboardData = new ClipboardData();
            if (!string.IsNullOrEmpty(_simulatedClipboardContent))
            {
                clipboardData.Text = _simulatedClipboardContent;
            }

            _loggingService.LogInfo($"TestClipboardInteractionService: Contenu simulé récupéré - Texte: '{_simulatedClipboardContent?.Substring(0, Math.Min(50, _simulatedClipboardContent?.Length ?? 0))}...'");

            return clipboardData;
        }

        public async Task<bool> SetClipboardContentAsync(string text)
        {
            _loggingService.LogInfo($"TestClipboardInteractionService: Simulation de la copie vers le clipboard - Texte: '{text?.Substring(0, Math.Min(50, text?.Length ?? 0))}...'");

            // Simuler l'opération asynchrone
            await Task.Delay(10);

            // Stocker le contenu simulé
            _simulatedClipboardContent = text ?? string.Empty;

            _loggingService.LogInfo("TestClipboardInteractionService: Copie vers clipboard simulée avec succès");

            return true; // Toujours réussir dans les tests
        }

        public async Task<string?> GetTextAsync()
        {
            _loggingService.LogInfo("TestClipboardInteractionService: Simulation de la lecture du texte du clipboard");

            // Simuler l'opération asynchrone
            await Task.Delay(10);

            _loggingService.LogInfo($"TestClipboardInteractionService: Lecture texte simulée - Contenu: '{_simulatedClipboardContent?.Substring(0, Math.Min(50, _simulatedClipboardContent?.Length ?? 0))}...'");

            return _simulatedClipboardContent;
        }

        public bool ContainsText()
        {
            _loggingService.LogInfo("TestClipboardInteractionService: Simulation de la vérification de contenu texte");
            return !string.IsNullOrEmpty(_simulatedClipboardContent);
        }

        public bool ContainsImage()
        {
            _loggingService.LogInfo("TestClipboardInteractionService: Simulation de la vérification de contenu image");
            return false; // Toujours false pour les tests
        }

        public async Task<BitmapSource?> GetImageAsync()
        {
            _loggingService.LogInfo("TestClipboardInteractionService: Simulation de la récupération d'image du clipboard");

            // Simuler l'opération asynchrone
            await Task.Delay(10);

            _loggingService.LogInfo("TestClipboardInteractionService: Aucune image simulée disponible");

            return null; // Toujours null pour les tests
        }

        public bool ContainsFileDropList()
        {
            _loggingService.LogInfo("TestClipboardInteractionService: Simulation de la vérification de contenu fichiers");
            return false; // Toujours false pour les tests
        }

        public async Task<StringCollection?> GetFileDropListAsync()
        {
            _loggingService.LogInfo("TestClipboardInteractionService: Simulation de la récupération de liste de fichiers");

            // Simuler l'opération asynchrone
            await Task.Delay(10);

            _loggingService.LogInfo("TestClipboardInteractionService: Aucune liste de fichiers simulée disponible");

            return null; // Toujours null pour les tests
        }

        /// <summary>
        /// Méthode de test pour définir le contenu simulé du clipboard
        /// </summary>
        public void SetSimulatedContent(string content)
        {
            _simulatedClipboardContent = content ?? string.Empty;
            _loggingService.LogInfo($"TestClipboardInteractionService: Contenu simulé défini - '{content?.Substring(0, Math.Min(50, content?.Length ?? 0))}...'");
        }
    }
}
