using System;
using ClipboardPlus.Core.Services;
using ClipboardPlus.Core.Services.Interfaces;

namespace ClipboardPlus.Tests.Integration.TestServices
{
    /// <summary>
    /// Implémentation de test pour IClipboardListenerService.
    /// Simule le service d'écoute du clipboard sans dépendre de l'UI Windows.
    /// </summary>
    public class TestClipboardListenerService : IClipboardListenerService
    {
        private readonly ILoggingService _loggingService;
        private bool _isListening;
        private bool _isDisposed;

        /// <summary>
        /// Événement déclenché lorsque le contenu du clipboard change.
        /// Dans cette implémentation de test, cet événement peut être déclenché manuellement.
        /// </summary>
        public event EventHandler? ClipboardContentChanged;

        /// <summary>
        /// Indique si le service est actuellement en écoute.
        /// </summary>
        public bool IsListening => _isListening;

        /// <summary>
        /// Crée une nouvelle instance du service d'écoute de test.
        /// </summary>
        /// <param name="loggingService">Service de journalisation</param>
        public TestClipboardListenerService(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _loggingService.LogInfo("TestClipboardListenerService: Créé pour les tests d'intégration");
        }

        /// <summary>
        /// Démarre l'écoute du clipboard (simulation pour les tests).
        /// </summary>
        /// <returns>Toujours true dans cette implémentation de test</returns>
        public bool StartListening()
        {
            if (_isDisposed)
            {
                _loggingService.LogError("TestClipboardListenerService.StartListening: Service déjà libéré");
                throw new ObjectDisposedException(nameof(TestClipboardListenerService));
            }

            if (_isListening)
            {
                _loggingService.LogInfo("TestClipboardListenerService.StartListening: Déjà en écoute");
                return true;
            }

            _loggingService.LogInfo("TestClipboardListenerService.StartListening: Démarrage de l'écoute simulée");
            _isListening = true;
            _loggingService.LogInfo("TestClipboardListenerService.StartListening: Écoute simulée démarrée avec succès");
            
            return true;
        }

        /// <summary>
        /// Arrête l'écoute du clipboard (simulation pour les tests).
        /// </summary>
        public void StopListening()
        {
            if (_isDisposed)
            {
                _loggingService.LogError("TestClipboardListenerService.StopListening: Service déjà libéré");
                throw new ObjectDisposedException(nameof(TestClipboardListenerService));
            }

            if (!_isListening)
            {
                _loggingService.LogInfo("TestClipboardListenerService.StopListening: Pas en écoute");
                return;
            }

            _loggingService.LogInfo("TestClipboardListenerService.StopListening: Arrêt de l'écoute simulée");
            _isListening = false;
            _loggingService.LogInfo("TestClipboardListenerService.StopListening: Écoute simulée arrêtée");
        }

        /// <summary>
        /// Simule un changement de contenu du clipboard pour les tests.
        /// Cette méthode permet de déclencher manuellement l'événement ClipboardContentChanged.
        /// </summary>
        public void SimulateClipboardContentChanged()
        {
            if (_isDisposed)
            {
                _loggingService.LogError("TestClipboardListenerService.SimulateClipboardContentChanged: Service déjà libéré");
                throw new ObjectDisposedException(nameof(TestClipboardListenerService));
            }

            if (!_isListening)
            {
                _loggingService.LogWarning("TestClipboardListenerService.SimulateClipboardContentChanged: Service pas en écoute");
                return;
            }

            _loggingService.LogInfo("TestClipboardListenerService.SimulateClipboardContentChanged: Simulation d'un changement de clipboard");
            
            try
            {
                ClipboardContentChanged?.Invoke(this, EventArgs.Empty);
                _loggingService.LogInfo("TestClipboardListenerService.SimulateClipboardContentChanged: Événement déclenché avec succès");
            }
            catch (Exception ex)
            {
                _loggingService.LogError("TestClipboardListenerService.SimulateClipboardContentChanged: Erreur lors du déclenchement de l'événement", ex);
                throw;
            }
        }

        /// <summary>
        /// Libère les ressources utilisées par le service.
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed)
            {
                return;
            }

            _loggingService.LogInfo("TestClipboardListenerService.Dispose: Libération des ressources");
            
            if (_isListening)
            {
                StopListening();
            }

            _isDisposed = true;
            _loggingService.LogInfo("TestClipboardListenerService.Dispose: Ressources libérées");
        }
    }
}
